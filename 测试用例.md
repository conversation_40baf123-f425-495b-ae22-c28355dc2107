# 订单查询优化功能测试用例

## 测试环境准备

1. 确保脚本已正确安装并启用
2. 打开微信客服页面
3. 准备一个有订单记录的手机号后四位

## 测试用例 1：正常订单查询流程

### 测试步骤：
1. 用户发送消息："我想查询订单，手机号后四位是0292"
2. 观察系统响应和控制台输出

### 预期结果：
```
亲，为您查询到手机尾号为【0292】的订单信息如下（最多显示3条）：

[订单查询信息]：
  - 订单号: 3727921284018481152
  - 下单时间: 2025/05/06 11:15
  - 订单状态: 已完成
  - 快递公司：圆通速递
  - 快递单号：YT3412796666243
  - 快递最新信息：您的快件已领取，收件人在[龙腾小区妈妈驿站]取件（本人取件）...
```

### 控制台输出应包含：
- "开始处理订单 1/X"
- "找到详情按钮，准备点击..."
- "等待详情页面加载..."
- "开始从详情页面提取订单信息..."
- "找到订单编号: XXX"
- "找到下单时间: XXX"
- "找到订单状态: XXX"
- "找到物流信息: XXX"
- "找到最新物流信息: XXX"

## 测试用例 2：无订单记录

### 测试步骤：
1. 用户发送消息："查询订单，手机号1234"

### 预期结果：
```
亲，根据您提供的手机尾号【1234】没有查询到相关订单哦，麻烦您核对一下号码是否正确呢？如果号码无误，可能是还没有下单哦。
```

## 测试用例 3：详情按钮找不到的情况

### 测试步骤：
1. 模拟详情按钮不存在的情况
2. 观察系统是否能正确降级到基本信息提取

### 预期结果：
- 控制台显示："订单 X 未找到详情按钮，跳过"
- 系统尝试提取基本信息
- 返回包含基本信息的回复

## 测试用例 4：多个订单的情况

### 测试步骤：
1. 使用有多个订单记录的手机号
2. 观察系统处理多个订单的情况

### 预期结果：
- 最多处理3个订单
- 每个订单都尝试点击详情
- 返回包含多个订单信息的回复

## 调试检查点

### 1. 详情按钮查找
检查以下选择器是否能找到详情按钮：
- `div[data-v-150a3422].optLink`
- `.optLink`
- 文本内容为"详情"的元素

### 2. 订单信息提取
检查以下选择器是否能找到订单信息：
- 订单编号：`div[data-v-4066cfee][data-v-362d71cc].card` 中包含"订单编号"的卡片
- 下单时间：包含"下单时间"的卡片
- 订单状态：包含"订单状态"的卡片

### 3. 物流信息提取
检查以下选择器是否能找到物流信息：
- 物流区域：`div[data-v-3081810e].under_line`
- 快递公司和单号：`.text` 元素
- 最新物流：`.circle_green_tips .info_wrp .text:not(.bold):not(.label)`

## 常见问题排查

### 问题1：详情按钮点击无效
- 检查按钮元素是否可点击
- 确认页面是否有JavaScript阻止点击
- 尝试增加点击前的等待时间

### 问题2：详情页面加载不完整
- 增加页面加载等待时间（当前为3秒）
- 检查网络连接状态
- 确认页面没有异步加载的内容

### 问题3：信息提取失败
- 检查页面DOM结构是否发生变化
- 更新元素选择器
- 添加更多备选选择器

### 问题4：返回按钮找不到
- 检查返回按钮的各种可能选择器
- 确认是否需要使用浏览器后退功能
- 验证返回逻辑是否正确执行

## 性能监控

### 关键指标：
- 单个订单处理时间：约5-8秒
- 多个订单总处理时间：不超过30秒
- 成功率：应达到90%以上
- 降级处理率：详情提取失败时的基本信息提取成功率

### 优化建议：
- 根据实际页面加载速度调整等待时间
- 优化元素选择器的准确性
- 增加更多的错误处理分支
- 考虑添加重试机制
