<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单查询优化功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e1e8f0;
            border-radius: 8px;
            background: #fafcff;
        }
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background: #fff5f5;
            border-left: 4px solid #ff6b6b;
        }
        .after {
            background: #f0fff4;
            border-left: 4px solid #51cf66;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }
        .step-list li {
            counter-increment: step-counter;
            padding: 10px 0;
            position: relative;
            padding-left: 40px;
        }
        .step-list li:before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 10px;
            background: #007bff;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 订单查询流程优化演示</h1>
        
        <div class="demo-section">
            <div class="demo-title">🎯 优化目标</div>
            <p>将原有的基础订单查询升级为包含完整物流信息的详细查询系统</p>
            
            <div class="before-after">
                <div class="before">
                    <h4>优化前 ❌</h4>
                    <ul class="feature-list">
                        <li>只能获取基本订单信息</li>
                        <li>缺少物流详情</li>
                        <li>用户需要额外询问快递信息</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>优化后 ✅</h4>
                    <ul class="feature-list">
                        <li>自动点击详情按钮</li>
                        <li>提取完整订单和物流信息</li>
                        <li>一次查询获得所有信息</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">🔄 新的查询流程</div>
            <ol class="step-list">
                <li>用户提供手机号后四位</li>
                <li>系统在新标签页打开订单列表</li>
                <li>输入手机号并点击查询</li>
                <li><span class="highlight">自动点击每个订单的"详情"按钮</span></li>
                <li><span class="highlight">进入详情页面提取完整信息</span></li>
                <li><span class="highlight">提取物流信息和最新状态</span></li>
                <li>返回包含完整信息的回复</li>
            </ol>
        </div>

        <div class="demo-section">
            <div class="demo-title">📋 回复模板对比</div>
            
            <div class="before-after">
                <div class="before">
                    <h4>原回复格式</h4>
                    <div class="code-block">
亲，为您查询到手机尾号为【0292】的订单信息如下：

[订单 1]：
  - 订单号: 3727921284018481152
  - 下单时间: 2025/05/06 1115
  - 订单状态: 已完成
                    </div>
                </div>
                <div class="after">
                    <h4>新回复格式</h4>
                    <div class="code-block">
亲，为您查询到手机尾号为【0292】的订单信息如下：

[订单查询信息]：
  - 订单号: 3727921284018481152
  - 下单时间: 2025/05/06 11:15
  - 订单状态: 已完成
  - <span class="highlight">快递公司：圆通速递</span>
  - <span class="highlight">快递单号：YT3412796666243</span>
  - <span class="highlight">快递最新信息：您的快件已领取...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">🛠️ 技术实现亮点</div>
            <ul class="feature-list">
                <li><strong>精确元素匹配</strong>：根据实际DOM结构优化选择器</li>
                <li><strong>智能降级处理</strong>：详情提取失败时自动使用基本信息</li>
                <li><strong>完善错误处理</strong>：每个步骤都有异常捕获和处理</li>
                <li><strong>调试信息丰富</strong>：控制台输出详细的执行过程</li>
                <li><strong>性能优化</strong>：最多处理3个订单，避免超时</li>
                <li><strong>用户体验</strong>：保持原有交互方式不变</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="demo-title">🔍 关键代码片段</div>
            
            <h4>详情按钮查找逻辑</h4>
            <div class="code-block">
// 根据提供的元素代码精确匹配详情按钮
const detailButton = orderContainer.querySelector('div[data-v-150a3422].optLink') || 
                   orderContainer.querySelector('.optLink') ||
                   Array.from(orderContainer.querySelectorAll('div')).find(el => 
                       el.textContent && el.textContent.trim() === '详情'
                   );
            </div>

            <h4>订单信息提取逻辑</h4>
            <div class="code-block">
// 订单编号提取
const orderIdCard = Array.from(shadowRoot.querySelectorAll('div[data-v-4066cfee][data-v-362d71cc].card'))
    .find(card => {
        const label = card.querySelector('div[data-v-4066cfee].label');
        return label && label.textContent.includes('订单编号');
    });
            </div>

            <h4>物流信息提取逻辑</h4>
            <div class="code-block">
// 物流信息提取
const logisticsSection = shadowRoot.querySelector('div[data-v-3081810e].under_line');
const latestLogisticsEl = shadowRoot.querySelector('.circle_green_tips .info_wrp .text:not(.bold):not(.label)');
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">📊 预期效果</div>
            <div class="before-after">
                <div class="before">
                    <h4>用户体验提升</h4>
                    <ul class="feature-list">
                        <li>减少用户询问次数</li>
                        <li>提供完整订单信息</li>
                        <li>包含实时物流状态</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>客服效率提升</h4>
                    <ul class="feature-list">
                        <li>自动化信息收集</li>
                        <li>减少手动查询工作</li>
                        <li>提高服务质量</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
